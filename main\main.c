
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_bt.h"
#include "driver/gpio.h"
#include "esp_wifi.h"

#include "esp_gap_ble_api.h"
#include "esp_gatts_api.h"
#include "esp_bt_main.h"
#include "esp_bt_device.h"

#include "esp_gatt_common_api.h"
#include "ble_gap.h"
#include "ble_gatt.h"
#include "wifi_ap.h"
#include "fs_mount.h"
#include "file_server.h"

#define BLE_SLAVE_TAG "BLE_SLAVE"
#define POWER_DETECT_GPIO GPIO_NUM_10
#define GPIO_CHECK_INTERVAL_MS 1000

// 全局变量
static bool wifi_is_running = false;
static bool file_server_is_running = false;
static bool storage_is_mounted = false;
static const char *base_path = "/data";

// 初始化GPIO
void gpio_init_power_detect()
{
    gpio_config_t io_conf = {
        .pin_bit_mask = (1ULL << POWER_DETECT_GPIO),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_ENABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE,
        .intr_type = GPIO_INTR_DISABLE
    };
    gpio_config(&io_conf);
    ESP_LOGI(BLE_SLAVE_TAG, "GPIO%d initialized for power detection", POWER_DETECT_GPIO);
}

// 启动WiFi和文件服务器
void start_wifi_and_file_server()
{
    if (wifi_is_running) {
        ESP_LOGW(BLE_SLAVE_TAG, "WiFi is already running");
        return;
    }

    ESP_LOGI(BLE_SLAVE_TAG, "Starting WiFi and file server (external power detected)");

    esp_err_t ret = wifi_init_ap();
    if (ret == ESP_OK) {
        wifi_is_running = true;
        ESP_LOGI(BLE_SLAVE_TAG, "WiFi AP initialization completed");

        // 挂载文件系统
        if (!storage_is_mounted) {
            ret = example_mount_storage(base_path);
            if (ret == ESP_OK) {
                storage_is_mounted = true;
            } else {
                ESP_LOGE(BLE_SLAVE_TAG, "Failed to mount storage");
                return;
            }
        }

        // 启动文件服务器
        if (!file_server_is_running) {
            ret = example_start_file_server(base_path);
            if (ret == ESP_OK) {
                file_server_is_running = true;
                ESP_LOGI(BLE_SLAVE_TAG, "File server started successfully");
            } else {
                ESP_LOGE(BLE_SLAVE_TAG, "Failed to start file server");
            }
        }
    } else {
        ESP_LOGE(BLE_SLAVE_TAG, "Failed to initialize WiFi AP");
    }
}

// 停止WiFi和文件服务器
void stop_wifi_and_file_server()
{
    if (!wifi_is_running) {
        ESP_LOGW(BLE_SLAVE_TAG, "WiFi is not running");
        return;
    }

    ESP_LOGI(BLE_SLAVE_TAG, "Stopping WiFi and file server (battery power detected)");

    // 停止文件服务器
    if (file_server_is_running) {
        esp_err_t ret = example_stop_file_server();
        if (ret == ESP_OK) {
            file_server_is_running = false;
            ESP_LOGI(BLE_SLAVE_TAG, "File server stopped successfully");
        } else {
            ESP_LOGE(BLE_SLAVE_TAG, "Failed to stop file server");
        }
    }

    // 卸载文件系统
    if (storage_is_mounted) {
        esp_err_t ret = example_unmount_storage();
        if (ret == ESP_OK) {
            storage_is_mounted = false;
            ESP_LOGI(BLE_SLAVE_TAG, "Storage unmounted successfully");
        } else {
            ESP_LOGE(BLE_SLAVE_TAG, "Failed to unmount storage");
        }
    }

    // 停止WiFi
    esp_err_t ret = wifi_stop_ap();
    if (ret == ESP_OK) {
        wifi_is_running = false;
        ESP_LOGI(BLE_SLAVE_TAG, "WiFi AP stopped successfully");
    } else {
        ESP_LOGE(BLE_SLAVE_TAG, "Failed to stop WiFi AP");
    }
}

// GPIO监控任务
void power_monitor_task(void *pvParameters)
{
    static bool last_power_state = false;
    bool current_power_state;

    ESP_LOGI(BLE_SLAVE_TAG, "Power monitor task started");

    while (1) {
        // 读取GPIO状态 (高电平=外部供电，低电平=电池供电)
        current_power_state = gpio_get_level(POWER_DETECT_GPIO);

        // 检测状态变化
        if (current_power_state != last_power_state) {
            ESP_LOGI(BLE_SLAVE_TAG, "Power state changed: %s",current_power_state ? "External Power" : "Battery Power");

            if (current_power_state) {
                // 高电平 - 外部供电，启动WiFi和文件服务器
                start_wifi_and_file_server();
            } 
            else {
                // 低电平 - 电池供电，停止WiFi和文件服务器
                stop_wifi_and_file_server();
            }

            last_power_state = current_power_state;
        }

        // 延时检查
        vTaskDelay(pdMS_TO_TICKS(GPIO_CHECK_INTERVAL_MS));
    }
}

// 初始化BLE
void ble_init()
{
    esp_err_t ret;

    // 此函数将BSS、数据和控制器的其他部分释放到堆中。总大小约为70KB
    // 如果您只打算使用BLE，调用esp_bt_controller_mem_release（esp_bt_MODE_CLASSIC_bt）可以释放BSS和经典蓝牙控制器消耗的数据。
    ret = esp_bt_controller_mem_release(ESP_BT_MODE_CLASSIC_BT);
    ESP_ERROR_CHECK(ret);

    // 使用官方默认的参数去初始化蓝牙底层的硬件资源，固定写法，不用深究
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret)
    {
        ESP_LOGE(BLE_SLAVE_TAG, "%s enable controller failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 控制器初始化完以后，还要显式"启用"，否则不会工作, 固定写法, 不用深究
    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret)
    {
        ESP_LOGE(BLE_SLAVE_TAG, "%s enable controller failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 初始化蓝牙协议栈
    // 控制器负责底层收发，协议栈负责 BLE 逻辑处理，比如 GATT 等  固定写法, 不用深究
    ret = esp_bluedroid_init();
    if (ret)
    {
        ESP_LOGE(BLE_SLAVE_TAG, "%s init bluetooth failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 使能蓝牙协议栈
    // 初始化只是分配资源，启动才是真正开始运行  固定写法, 不用深究
    ret = esp_bluedroid_enable();
    if (ret)
    {
        ESP_LOGE(BLE_SLAVE_TAG, "%s enable bluetooth failed: %s", __func__, esp_err_to_name(ret));
        return;
    }

    // 初始化 GAP 层
    ret = ble_gap_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_SLAVE_TAG, "GAP init failed, error code = 0x%x", ret);
        return;
    }

    // 初始化 GATT 层
    ret = ble_gatt_init();
    if (ret != ESP_OK)
    {
        ESP_LOGE(BLE_SLAVE_TAG, "GATT init failed, error code = 0x%x", ret);
        return;
    }
}

// 初始化WiFi AP (保留原函数用于兼容性，但现在由GPIO控制)
void wifi_init()
{
    ESP_LOGW(BLE_SLAVE_TAG, "WiFi initialization is now controlled by GPIO%d power detection", POWER_DETECT_GPIO);
    ESP_LOGW(BLE_SLAVE_TAG, "High level = External power (WiFi ON), Low level = Battery power (WiFi OFF)");
}

void app_main(void)
{
    esp_err_t ret;

    // NVS（Non-Volatile Storage） 是一种非易失性存储系统，主要用于在设备掉电或重启后保留数据
    // 通常是以 key-value（键值对） 的形式存储数据, 保存配置参数（如 WiFi 名称、密码）
    ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND)
    {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 初始化GPIO用于电源检测
    gpio_init_power_detect();

    // 使用BLE与主从通讯
    ble_init();

    // 创建电源监控任务
    xTaskCreate(power_monitor_task, "power_monitor", 4096, NULL, 5, NULL);

    ESP_LOGI(BLE_SLAVE_TAG, "System initialized. WiFi will be controlled by GPIO%d power detection.", POWER_DETECT_GPIO);
}
