{"version": "1.2", "project_name": "ble_slave", "project_version": "d34347b", "project_path": "E:/esp32_space/ble_mill_monitor_slave", "idf_path": "E:/esp/v5.3.3/esp-idf", "build_dir": "E:/esp32_space/ble_mill_monitor_slave/build", "config_file": "E:/esp32_space/ble_mill_monitor_slave/sdkconfig", "config_defaults": "E:/esp32_space/ble_mill_monitor_slave/sdkconfig.defaults", "bootloader_elf": "E:/esp32_space/ble_mill_monitor_slave/build/bootloader/bootloader.elf", "app_elf": "ble_slave.elf", "app_bin": "ble_slave.bin", "build_type": "flash_app", "git_revision": "v5.3.3", "target": "esp32c3", "rev": "", "min_rev": "3", "max_rev": "199", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "riscv32-esp-elf-", "c_compiler": "E:/esp_tool/.espressif/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/riscv32-esp-elf-gcc.exe", "config_environment": {"COMPONENT_KCONFIGS": "E:/esp/v5.3.3/esp-idf/components/app_trace/Kconfig;E:/esp/v5.3.3/esp-idf/components/bt/Kconfig;E:/esp/v5.3.3/esp-idf/components/console/Kconfig;E:/esp/v5.3.3/esp-idf/components/driver/Kconfig;E:/esp/v5.3.3/esp-idf/components/efuse/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp-tls/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_adc/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_coex/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_common/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_ana_cmpr/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_cam/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_dac/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_isp/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_jpeg/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_ledc/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_mcpwm/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_parlio/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_pcnt/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_sdm/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_spi/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_touch_sens/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_tsens/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_uart/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_eth/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_event/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_gdbstub/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_hid/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_http_client/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_http_server/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_https_ota/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_https_server/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_hw_support/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_lcd/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_netif/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_partition/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_phy/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_pm/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_psram/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_ringbuf/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_system/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_timer/Kconfig;E:/esp/v5.3.3/esp-idf/components/esp_wifi/Kconfig;E:/esp/v5.3.3/esp-idf/components/espcoredump/Kconfig;E:/esp/v5.3.3/esp-idf/components/fatfs/Kconfig;E:/esp/v5.3.3/esp-idf/components/freertos/Kconfig;E:/esp/v5.3.3/esp-idf/components/hal/Kconfig;E:/esp/v5.3.3/esp-idf/components/heap/Kconfig;E:/esp/v5.3.3/esp-idf/components/ieee802154/Kconfig;E:/esp/v5.3.3/esp-idf/components/log/Kconfig;E:/esp/v5.3.3/esp-idf/components/lwip/Kconfig;E:/esp/v5.3.3/esp-idf/components/mbedtls/Kconfig;E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/Kconfig;E:/esp/v5.3.3/esp-idf/components/newlib/Kconfig;E:/esp/v5.3.3/esp-idf/components/nvs_flash/Kconfig;E:/esp/v5.3.3/esp-idf/components/nvs_sec_provider/Kconfig;E:/esp/v5.3.3/esp-idf/components/openthread/Kconfig;E:/esp/v5.3.3/esp-idf/components/protocomm/Kconfig;E:/esp/v5.3.3/esp-idf/components/pthread/Kconfig;E:/esp/v5.3.3/esp-idf/components/soc/Kconfig;E:/esp/v5.3.3/esp-idf/components/spi_flash/Kconfig;E:/esp/v5.3.3/esp-idf/components/spiffs/Kconfig;E:/esp/v5.3.3/esp-idf/components/tcp_transport/Kconfig;E:/esp/v5.3.3/esp-idf/components/ulp/Kconfig;E:/esp/v5.3.3/esp-idf/components/unity/Kconfig;E:/esp/v5.3.3/esp-idf/components/usb/Kconfig;E:/esp/v5.3.3/esp-idf/components/vfs/Kconfig;E:/esp/v5.3.3/esp-idf/components/wear_levelling/Kconfig;E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "E:/esp/v5.3.3/esp-idf/components/bootloader/Kconfig.projbuild;E:/esp/v5.3.3/esp-idf/components/esp_app_format/Kconfig.projbuild;E:/esp/v5.3.3/esp-idf/components/esp_rom/Kconfig.projbuild;E:/esp/v5.3.3/esp-idf/components/esptool_py/Kconfig.projbuild;E:/esp/v5.3.3/esp-idf/components/partition_table/Kconfig.projbuild"}, "common_component_reqs": ["cxx", "newlib", "freertos", "esp_hw_support", "heap", "log", "soc", "hal", "esp_rom", "esp_common", "esp_system", "riscv"], "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bt", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_bootloader_format", "esp_coex", "esp_common", "esp_driver_ana_cmpr", "esp_driver_cam", "esp_driver_dac", "esp_driver_gpio", "esp_driver_gptimer", "esp_driver_i2c", "esp_driver_i2s", "esp_driver_isp", "esp_driver_jpeg", "esp_driver_ledc", "esp_driver_mcpwm", "esp_driver_parlio", "esp_driver_pcnt", "esp_driver_ppa", "esp_driver_rmt", "esp_driver_sdio", "esp_driver_sdm", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_spi", "esp_driver_touch_sens", "esp_driver_tsens", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_system", "esp_timer", "esp_vfs_console", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "idf_test", "ieee802154", "json", "log", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "nvs_sec_provider", "openthread", "partition_table", "protobuf-c", "protocomm", "pthread", "riscv", "sdmmc", "soc", "spi_flash", "spiffs", "tcp_transport", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", ""], "build_component_paths": ["E:/esp/v5.3.3/esp-idf/components/app_trace", "E:/esp/v5.3.3/esp-idf/components/app_update", "E:/esp/v5.3.3/esp-idf/components/bootloader", "E:/esp/v5.3.3/esp-idf/components/bootloader_support", "E:/esp/v5.3.3/esp-idf/components/bt", "E:/esp/v5.3.3/esp-idf/components/cmock", "E:/esp/v5.3.3/esp-idf/components/console", "E:/esp/v5.3.3/esp-idf/components/cxx", "E:/esp/v5.3.3/esp-idf/components/driver", "E:/esp/v5.3.3/esp-idf/components/efuse", "E:/esp/v5.3.3/esp-idf/components/esp-tls", "E:/esp/v5.3.3/esp-idf/components/esp_adc", "E:/esp/v5.3.3/esp-idf/components/esp_app_format", "E:/esp/v5.3.3/esp-idf/components/esp_bootloader_format", "E:/esp/v5.3.3/esp-idf/components/esp_coex", "E:/esp/v5.3.3/esp-idf/components/esp_common", "E:/esp/v5.3.3/esp-idf/components/esp_driver_ana_cmpr", "E:/esp/v5.3.3/esp-idf/components/esp_driver_cam", "E:/esp/v5.3.3/esp-idf/components/esp_driver_dac", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s", "E:/esp/v5.3.3/esp-idf/components/esp_driver_isp", "E:/esp/v5.3.3/esp-idf/components/esp_driver_jpeg", "E:/esp/v5.3.3/esp-idf/components/esp_driver_ledc", "E:/esp/v5.3.3/esp-idf/components/esp_driver_mcpwm", "E:/esp/v5.3.3/esp-idf/components/esp_driver_parlio", "E:/esp/v5.3.3/esp-idf/components/esp_driver_pcnt", "E:/esp/v5.3.3/esp-idf/components/esp_driver_ppa", "E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt", "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdio", "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdm", "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdmmc", "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi", "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi", "E:/esp/v5.3.3/esp-idf/components/esp_driver_touch_sens", "E:/esp/v5.3.3/esp-idf/components/esp_driver_tsens", "E:/esp/v5.3.3/esp-idf/components/esp_driver_uart", "E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag", "E:/esp/v5.3.3/esp-idf/components/esp_eth", "E:/esp/v5.3.3/esp-idf/components/esp_event", "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub", "E:/esp/v5.3.3/esp-idf/components/esp_hid", "E:/esp/v5.3.3/esp-idf/components/esp_http_client", "E:/esp/v5.3.3/esp-idf/components/esp_http_server", "E:/esp/v5.3.3/esp-idf/components/esp_https_ota", "E:/esp/v5.3.3/esp-idf/components/esp_https_server", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support", "E:/esp/v5.3.3/esp-idf/components/esp_lcd", "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl", "E:/esp/v5.3.3/esp-idf/components/esp_mm", "E:/esp/v5.3.3/esp-idf/components/esp_netif", "E:/esp/v5.3.3/esp-idf/components/esp_netif_stack", "E:/esp/v5.3.3/esp-idf/components/esp_partition", "E:/esp/v5.3.3/esp-idf/components/esp_phy", "E:/esp/v5.3.3/esp-idf/components/esp_pm", "E:/esp/v5.3.3/esp-idf/components/esp_psram", "E:/esp/v5.3.3/esp-idf/components/esp_ringbuf", "E:/esp/v5.3.3/esp-idf/components/esp_rom", "E:/esp/v5.3.3/esp-idf/components/esp_system", "E:/esp/v5.3.3/esp-idf/components/esp_timer", "E:/esp/v5.3.3/esp-idf/components/esp_vfs_console", "E:/esp/v5.3.3/esp-idf/components/esp_wifi", "E:/esp/v5.3.3/esp-idf/components/espcoredump", "E:/esp/v5.3.3/esp-idf/components/esptool_py", "E:/esp/v5.3.3/esp-idf/components/fatfs", "E:/esp/v5.3.3/esp-idf/components/freertos", "E:/esp/v5.3.3/esp-idf/components/hal", "E:/esp/v5.3.3/esp-idf/components/heap", "E:/esp/v5.3.3/esp-idf/components/http_parser", "E:/esp/v5.3.3/esp-idf/components/idf_test", "E:/esp/v5.3.3/esp-idf/components/ieee802154", "E:/esp/v5.3.3/esp-idf/components/json", "E:/esp/v5.3.3/esp-idf/components/log", "E:/esp/v5.3.3/esp-idf/components/lwip", "E:/esp32_space/ble_mill_monitor_slave/main", "E:/esp/v5.3.3/esp-idf/components/mbedtls", "E:/esp/v5.3.3/esp-idf/components/mqtt", "E:/esp/v5.3.3/esp-idf/components/newlib", "E:/esp/v5.3.3/esp-idf/components/nvs_flash", "E:/esp/v5.3.3/esp-idf/components/nvs_sec_provider", "E:/esp/v5.3.3/esp-idf/components/openthread", "E:/esp/v5.3.3/esp-idf/components/partition_table", "E:/esp/v5.3.3/esp-idf/components/protobuf-c", "E:/esp/v5.3.3/esp-idf/components/protocomm", "E:/esp/v5.3.3/esp-idf/components/pthread", "E:/esp/v5.3.3/esp-idf/components/riscv", "E:/esp/v5.3.3/esp-idf/components/sdmmc", "E:/esp/v5.3.3/esp-idf/components/soc", "E:/esp/v5.3.3/esp-idf/components/spi_flash", "E:/esp/v5.3.3/esp-idf/components/spiffs", "E:/esp/v5.3.3/esp-idf/components/tcp_transport", "E:/esp/v5.3.3/esp-idf/components/ulp", "E:/esp/v5.3.3/esp-idf/components/unity", "E:/esp/v5.3.3/esp-idf/components/usb", "E:/esp/v5.3.3/esp-idf/components/vfs", "E:/esp/v5.3.3/esp-idf/components/wear_levelling", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant", ""], "build_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/app_trace", "type": "LIBRARY", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/app_trace/libapp_trace.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/app_trace/app_trace.c", "E:/esp/v5.3.3/esp-idf/components/app_trace/app_trace_util.c", "E:/esp/v5.3.3/esp-idf/components/app_trace/host_file_io.c", "E:/esp/v5.3.3/esp-idf/components/app_trace/port/port_uart.c"], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/app_update", "type": "LIBRARY", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/app_update/libapp_update.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/app_update/esp_ota_ops.c", "E:/esp/v5.3.3/esp-idf/components/app_update/esp_ota_app_desc.c"], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/bootloader", "type": "CONFIG_ONLY", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/bootloader_support", "type": "LIBRARY", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/bootloader_support/libbootloader_support.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_common.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_common_loader.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_clock_init.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_mem.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_random.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_efuse.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/flash_encrypt.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/secure_boot.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_random_esp32c3.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/bootloader_flash/src/flash_qio_mode.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/bootloader_flash/src/bootloader_flash_config_esp32c3.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/bootloader_utility.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/flash_partitions.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/esp_image_format.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/idf/bootloader_sha.c", "E:/esp/v5.3.3/esp-idf/components/bootloader_support/src/esp32c3/secure_boot_secure_features.c"], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/bt", "type": "LIBRARY", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/bt/libbt.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/bt/controller/esp32c3/bt.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/btc/core/btc_alarm.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/api/esp_blufi_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/hci_log/bt_hci_log.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/btc/core/btc_manage.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/btc/core/btc_task.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/btc/profile/esp/blufi/blufi_prf.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/btc/profile/esp/blufi/blufi_protocol.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/alarm.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/allocator.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/buffer.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/config.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/fixed_queue.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/pkt_queue.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/fixed_pkt_queue.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/future.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/hash_functions.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/hash_map.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/list.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/mutex.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/thread.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/osi.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/osi/semaphore.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/mem/bt_osi_mem.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/ble_log/ble_log_spi_out.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_a2dp_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_avrc_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_bluedroid_hci.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_bt_device.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_bt_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_gap_ble_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_gap_bt_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_gatt_common_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_gattc_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_gatts_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_hidd_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_hidh_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_hf_ag_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_hf_client_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_spp_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_sdp_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/api/esp_l2cap_bt_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/ar/bta_ar.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_aact.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_cfg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_ci.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_sbc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/av/bta_av_ssm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_cfg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_ci.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_pm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_sco.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/dm/bta_dm_qos.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gatt_common.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_cache.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_ci.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gattc_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gatts_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gatts_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gatts_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gatts_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/gatt/bta_gatts_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hd/bta_hd_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hd/bta_hd_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hd/bta_hd_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hh/bta_hh_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hh/bta_hh_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hh/bta_hh_cfg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hh/bta_hh_le.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hh/bta_hh_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hh/bta_hh_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/jv/bta_jv_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/jv/bta_jv_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/jv/bta_jv_cfg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/jv/bta_jv_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_at.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_cfg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_cmd.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_rfc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_sco.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_ag/bta_ag_sdp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_at.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_cmd.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_rfc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_sco.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/hf_client/bta_hf_client_sdp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sdp/bta_sdp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sdp/bta_sdp_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sdp/bta_sdp_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sdp/bta_sdp_cfg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sys/bta_sys_conn.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sys/bta_sys_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/bta/sys/utl.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_ble_storage.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_config.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_dev.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_dm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_profile_queue.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_sec.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_sm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_storage.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/core/btc_util.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/a2dp/bta_av_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_control.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_sink.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_a2dp_source.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/a2dp/btc_av.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/avrc/btc_avrc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/avrc/bta_avrc_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hf_ag/bta_ag_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hf_ag/btc_hf_ag.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hf_client/btc_hf_client.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hf_client/bta_hf_client_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hid/btc_hd.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hid/btc_hh.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/hid/bta_hh_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gap/btc_gap_ble.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gap/btc_gap_bt.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gap/bta_gap_bt_co.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gatt_common.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gatt_util.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gattc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/gatt/btc_gatts.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/spp/btc_spp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/sdp/btc_sdp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/btc/profile/std/l2cap/btc_l2cap.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/device/bdaddr.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/device/controller.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/device/interop.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/alloc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/bitalloc-sbc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/bitalloc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/bitstream-decode.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/decoder-oina.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/decoder-private.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/decoder-sbc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/dequant.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/framing-sbc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/framing.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/oi_codec_version.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/synthesis-8-generated.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/synthesis-dct8.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/decoder/srce/synthesis-sbc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_analysis.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_dct.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_dct_coeffs.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_enc_bit_alloc_mono.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_enc_bit_alloc_ste.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_enc_coeffs.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_encoder.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/encoder/srce/sbc_packing.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/external/sbc/plc/sbc_plc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/hci/hci_audio.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/hci/hci_hal_h4.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/hci/hci_layer.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/hci/hci_packet_factory.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/hci/hci_packet_parser.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/hci/packet_fragmenter.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/main/bte_init.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/main/bte_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/a2dp/a2d_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/a2dp/a2d_sbc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avct/avct_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avct/avct_ccb.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avct/avct_l2c.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avct/avct_lcb.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avct/avct_lcb_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_ad.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_ccb.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_ccb_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_l2c.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_msg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_scb.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avdt/avdt_scb_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_bld_ct.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_bld_tg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_opt.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_pars_ct.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_pars_tg.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_sdp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/avrc/avrc_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/hid/hidd_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/hid/hidd_conn.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/hid/hidh_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/hid/hidh_conn.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_acl.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_addr.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_adv_filter.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_batchscan.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_bgconn.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_cont_energy.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_gap.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_5_gap.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_multi_adv.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_ble_privacy.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_dev.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_devctl.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_inq.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_pm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_sco.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btm/btm_sec.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btu/btu_hcif.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btu/btu_init.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/btu/btu_task.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gap/gap_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gap/gap_ble.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gap/gap_conn.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gap/gap_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/att_protocol.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_attr.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_auth.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_cl.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_db.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_sr.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_sr_hash.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/gatt/gatt_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/hcic/hciblecmds.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/hcic/hcicmds.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_ble.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_csm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_fcr.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_link.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_ucd.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2c_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/l2cap/l2cap_client.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/port_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/port_rfc.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/port_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/rfc_l2cap_if.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/rfc_mx_fsm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/rfc_port_fsm.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/rfc_port_if.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/rfc_ts_frames.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/rfcomm/rfc_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/sdp/sdp_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/sdp/sdp_db.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/sdp/sdp_discovery.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/sdp/sdp_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/sdp/sdp_server.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/sdp/sdp_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/aes.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/p_256_curvepara.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/p_256_ecc_pp.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/p_256_multprecision.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_act.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_api.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_br_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_cmac.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_keys.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_l2c.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_main.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/stack/smp/smp_utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/host/bluedroid/config/stack_config.c", "E:/esp/v5.3.3/esp-idf/components/bt/common/btc/profile/esp/blufi/bluedroid_host/esp_blufi.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/utils.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/sha256.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ecc.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ctr_prng.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ctr_mode.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/aes_decrypt.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/aes_encrypt.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ccm_mode.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ecc_dsa.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/cmac_mode.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ecc_dh.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/hmac_prng.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/ecc_platform_specific.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/hmac.c", "E:/esp/v5.3.3/esp-idf/components/bt/porting/ext/tinycrypt/src/cbc_mode.c"], "include_dirs": ["include/esp32c3/include", "common/osi/include", "common/api/include/api", "common/btc/profile/esp/blufi/include", "common/btc/profile/esp/include", "common/hci_log/include", "common/ble_log/include", "host/bluedroid/api/include/api", "porting/ext/tinycrypt/include"]}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/cmock", "type": "LIBRARY", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/cmock/libcmock.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/cmock/CMock/src/cmock.c"], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/console", "type": "LIBRARY", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/console/libconsole.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/console/commands.c", "E:/esp/v5.3.3/esp-idf/components/console/esp_console_common.c", "E:/esp/v5.3.3/esp-idf/components/console/split_argv.c", "E:/esp/v5.3.3/esp-idf/components/console/linenoise/linenoise.c", "E:/esp/v5.3.3/esp-idf/components/console/esp_console_repl_chip.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_cmd.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_date.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_dbl.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_dstr.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_end.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_file.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_hashtable.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_int.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_lit.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_rem.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_rex.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_str.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/arg_utils.c", "E:/esp/v5.3.3/esp-idf/components/console/argtable3/argtable3.c"], "include_dirs": ["E:/esp/v5.3.3/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/cxx", "type": "LIBRARY", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/cxx/libcxx.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/cxx/cxx_exception_stubs.cpp", "E:/esp/v5.3.3/esp-idf/components/cxx/cxx_guards.cpp", "E:/esp/v5.3.3/esp-idf/components/cxx/cxx_init.cpp"], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/driver", "type": "LIBRARY", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/driver/libdriver.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/driver/deprecated/adc_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/deprecated/adc_dma_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/deprecated/timer_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/i2c/i2c.c", "E:/esp/v5.3.3/esp-idf/components/driver/deprecated/i2s_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/deprecated/rmt_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/deprecated/sigma_delta_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/deprecated/rtc_temperature_legacy.c", "E:/esp/v5.3.3/esp-idf/components/driver/twai/twai.c"], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/efuse", "type": "LIBRARY", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/efuse/libefuse.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/efuse/esp32c3/esp_efuse_table.c", "E:/esp/v5.3.3/esp-idf/components/efuse/esp32c3/esp_efuse_fields.c", "E:/esp/v5.3.3/esp-idf/components/efuse/esp32c3/esp_efuse_rtc_calib.c", "E:/esp/v5.3.3/esp-idf/components/efuse/esp32c3/esp_efuse_utility.c", "E:/esp/v5.3.3/esp-idf/components/efuse/src/esp_efuse_api.c", "E:/esp/v5.3.3/esp-idf/components/efuse/src/esp_efuse_fields.c", "E:/esp/v5.3.3/esp-idf/components/efuse/src/esp_efuse_utility.c", "E:/esp/v5.3.3/esp-idf/components/efuse/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c", "E:/esp/v5.3.3/esp-idf/components/efuse/src/esp_efuse_startup.c"], "include_dirs": ["include", "esp32c3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp-tls", "type": "LIBRARY", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp-tls/libesp-tls.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp-tls/esp_tls.c", "E:/esp/v5.3.3/esp-idf/components/esp-tls/esp-tls-crypto/esp_tls_crypto.c", "E:/esp/v5.3.3/esp-idf/components/esp-tls/esp_tls_error_capture.c", "E:/esp/v5.3.3/esp-idf/components/esp-tls/esp_tls_platform_port.c", "E:/esp/v5.3.3/esp-idf/components/esp-tls/esp_tls_mbedtls.c"], "include_dirs": ["E:/esp/v5.3.3/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_adc", "type": "LIBRARY", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_adc/libesp_adc.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_oneshot.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_cali.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_cali_curve_fitting.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/deprecated/esp_adc_cal_common_legacy.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_continuous.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_monitor.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/gdma/adc_dma.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/adc_filter.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/esp32c3/curve_fitting_coefficients.c", "E:/esp/v5.3.3/esp-idf/components/esp_adc/deprecated/esp32c3/esp_adc_cal_legacy.c"], "include_dirs": ["include", "interface", "esp32c3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_app_format", "type": "LIBRARY", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_app_format/libesp_app_format.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_app_format/esp_app_desc.c"], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_bootloader_format", "type": "LIBRARY", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_bootloader_format/libesp_bootloader_format.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_bootloader_format/esp_bootloader_desc.c"], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_coex", "type": "LIBRARY", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_coex/libesp_coex.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_coex/src/coexist.c", "E:/esp/v5.3.3/esp-idf/components/esp_coex/src/lib_printf.c", "E:/esp/v5.3.3/esp-idf/components/esp_coex/esp32c3/esp_coex_adapter.c", "E:/esp/v5.3.3/esp-idf/components/esp_coex/src/coexist_debug_diagram.c", "E:/esp/v5.3.3/esp-idf/components/esp_coex/src/coexist_debug.c"], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_common", "type": "LIBRARY", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_common/libesp_common.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_common/src/esp_err_to_name.c"], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_ana_cmpr", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_cam", "type": "LIBRARY", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_cam/libesp_driver_cam.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_cam/esp_cam_ctlr.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_cam/dvp_share_ctrl.c"], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_dac", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio", "type": "LIBRARY", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_gpio/libesp_driver_gpio.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio/src/gpio.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio/src/gpio_glitch_filter_ops.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio/src/rtc_io.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio/src/dedic_gpio.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio/src/gpio_pin_glitch_filter.c"], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer", "type": "LIBRARY", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_gptimer/libesp_driver_gptimer.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer/src/gptimer.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer/src/gptimer_common.c"], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c", "type": "LIBRARY", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_i2c/libesp_driver_i2c.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c/i2c_master.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c/i2c_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c/i2c_slave.c"], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s", "type": "LIBRARY", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_i2s/libesp_driver_i2s.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_platform.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_std.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_pdm.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s/i2s_tdm.c"], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_isp", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_jpeg", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_ledc", "type": "LIBRARY", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_ledc/libesp_driver_ledc.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_ledc/src/ledc.c"], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_mcpwm", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_parlio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_pcnt", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_ppa", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt", "type": "LIBRARY", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_rmt/libesp_driver_rmt.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_encoder.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_rx.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt/src/rmt_tx.c"], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdio", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdm", "type": "LIBRARY", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_sdm/libesp_driver_sdm.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_sdm/src/sdm.c"], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdmmc", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi", "type": "LIBRARY", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_sdspi/libesp_driver_sdspi.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi/src/sdspi_crc.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi/src/sdspi_host.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi/src/sdspi_transaction.c"], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi", "type": "LIBRARY", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_spi/libesp_driver_spi.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_master.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_dma.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi/src/gpspi/spi_slave_hd.c"], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_touch_sens", "type": "CONFIG_ONLY", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_tsens", "type": "LIBRARY", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_tsens/libesp_driver_tsens.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_tsens/src/temperature_sensor.c"], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_uart", "type": "LIBRARY", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_uart/libesp_driver_uart.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_uart/src/uart.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_uart/src/uart_vfs.c"], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag", "type": "LIBRARY", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_driver_usb_serial_jtag/libesp_driver_usb_serial_jtag.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_connection_monitor.c", "E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag/src/usb_serial_jtag_vfs.c"], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_eth", "type": "LIBRARY", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_eth/libesp_eth.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_eth/src/esp_eth.c", "E:/esp/v5.3.3/esp-idf/components/esp_eth/src/phy/esp_eth_phy_802_3.c", "E:/esp/v5.3.3/esp-idf/components/esp_eth/src/esp_eth_netif_glue.c"], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_event", "type": "LIBRARY", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_event/libesp_event.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_event/default_event_loop.c", "E:/esp/v5.3.3/esp-idf/components/esp_event/esp_event.c", "E:/esp/v5.3.3/esp-idf/components/esp_event/esp_event_private.c"], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub", "type": "LIBRARY", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_gdbstub/libesp_gdbstub.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_gdbstub/src/gdbstub.c", "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub/src/gdbstub_transport.c", "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub/src/packet.c", "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub/src/port/riscv/gdbstub_riscv.c", "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub/src/port/riscv/rv_decode.c"], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_hid", "type": "LIBRARY", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_hid/libesp_hid.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_hid/src/esp_hidd.c", "E:/esp/v5.3.3/esp-idf/components/esp_hid/src/esp_hidh.c", "E:/esp/v5.3.3/esp-idf/components/esp_hid/src/esp_hid_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_hid/src/ble_hidd.c", "E:/esp/v5.3.3/esp-idf/components/esp_hid/src/ble_hidh.c", "E:/esp/v5.3.3/esp-idf/components/esp_hid/src/bt_hidh.c", "E:/esp/v5.3.3/esp-idf/components/esp_hid/src/bt_hidd.c"], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_http_client", "type": "LIBRARY", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_http_client/libesp_http_client.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_http_client/esp_http_client.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_client/lib/http_auth.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_client/lib/http_header.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_client/lib/http_utils.c"], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_http_server", "type": "LIBRARY", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_http_server/libesp_http_server.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/httpd_main.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/httpd_parse.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/httpd_sess.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/httpd_txrx.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/httpd_uri.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/httpd_ws.c", "E:/esp/v5.3.3/esp-idf/components/esp_http_server/src/util/ctrl_sock.c"], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_https_ota", "type": "LIBRARY", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_https_ota/libesp_https_ota.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_https_ota/src/esp_https_ota.c"], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_https_server", "type": "LIBRARY", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_https_server/libesp_https_server.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_https_server/src/https_server.c"], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_hw_support", "type": "LIBRARY", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_hw_support/libesp_hw_support.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_hw_support/cpu.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/esp_cpu_intr.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/esp_memory_utils.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/cpu_region_protect.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/esp_clk.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/clk_ctrl_os.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/hw_random.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/intr_alloc.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/mac_addr.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/periph_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/revision.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/rtc_module.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_modem.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_modes.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_console.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_usb.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_gpio.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_event.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/regi2c_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/esp_gpio_reserve.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sar_periph_ctrl_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/io_mux.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/esp_clk_tree.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp_clk_tree_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/dma/esp_dma_utils.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/dma/gdma_link.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/spi_share_hw_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/spi_bus_lock.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/clk_utils.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/adc_share_hw_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/dma/gdma.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/deprecated/gdma_legacy.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/dma/esp_async_memcpy.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/dma/async_memcpy_gdma.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/systimer.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/esp_hmac.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/esp_ds.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/mspi_timing_tuning.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/sleep_wake_stub.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/esp_clock_output.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/rtc_clk_init.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/rtc_clk.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/rtc_init.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/rtc_sleep.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/rtc_time.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/chip_info.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/esp_crypto_lock.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/sar_periph_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/adc2_init_cal.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp32c3/esp_memprot.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/port/esp_memprot_conv.c", "E:/esp/v5.3.3/esp-idf/components/esp_hw_support/lowpower/cpu_retention/port/esp32c3/sleep_cpu.c"], "include_dirs": ["include", "include/soc", "include/soc/esp32c3", "dma/include", "ldo/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_lcd", "type": "LIBRARY", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_lcd/libesp_lcd.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_io.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_nt35510.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_ssd1306.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_st7789.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/src/esp_lcd_panel_ops.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v1.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/i2c/esp_lcd_panel_io_i2c_v2.c", "E:/esp/v5.3.3/esp-idf/components/esp_lcd/spi/esp_lcd_panel_io_spi.c"], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl", "type": "LIBRARY", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_local_ctrl/libesp_local_ctrl.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_handler.c", "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl/proto-c/esp_local_ctrl.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_ble.c", "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl/src/esp_local_ctrl_transport_httpd.c"], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_mm", "type": "LIBRARY", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_mm/libesp_mm.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_mm/esp_mmu_map.c", "E:/esp/v5.3.3/esp-idf/components/esp_mm/port/esp32c3/ext_mem_layout.c", "E:/esp/v5.3.3/esp-idf/components/esp_mm/esp_cache.c", "E:/esp/v5.3.3/esp-idf/components/esp_mm/heap_align_hw.c"], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_netif", "type": "LIBRARY", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_netif/libesp_netif.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_netif/esp_netif_handlers.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/esp_netif_objects.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/esp_netif_defaults.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/lwip/esp_netif_lwip.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/lwip/esp_netif_sntp.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/lwip/esp_netif_lwip_defaults.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/lwip/netif/wlanif.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/lwip/netif/ethernetif.c", "E:/esp/v5.3.3/esp-idf/components/esp_netif/lwip/netif/esp_pbuf_ref.c"], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_netif_stack", "type": "CONFIG_ONLY", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_partition", "type": "LIBRARY", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "bootloader_support", "spi_flash", "app_update", "partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_partition/libesp_partition.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_partition/partition.c", "E:/esp/v5.3.3/esp-idf/components/esp_partition/partition_target.c"], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_phy", "type": "LIBRARY", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_phy/libesp_phy.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_phy/src/phy_override.c", "E:/esp/v5.3.3/esp-idf/components/esp_phy/src/lib_printf.c", "E:/esp/v5.3.3/esp-idf/components/esp_phy/src/phy_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_phy/src/phy_init.c", "E:/esp/v5.3.3/esp-idf/components/esp_phy/esp32c3/phy_init_data.c", "E:/esp/v5.3.3/esp-idf/components/esp_phy/src/btbb_init.c"], "include_dirs": ["include", "esp32c3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_pm", "type": "LIBRARY", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_pm/libesp_pm.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_pm/pm_locks.c", "E:/esp/v5.3.3/esp-idf/components/esp_pm/pm_trace.c", "E:/esp/v5.3.3/esp-idf/components/esp_pm/pm_impl.c"], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_psram", "type": "CONFIG_ONLY", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_ringbuf", "type": "LIBRARY", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_ringbuf/libesp_ringbuf.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_ringbuf/ringbuf.c"], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_rom", "type": "LIBRARY", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_rom/libesp_rom.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_crc.c", "E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_sys.c", "E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_uart.c", "E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_spiflash.c", "E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_efuse.c", "E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_gpio.c", "E:/esp/v5.3.3/esp-idf/components/esp_rom/patches/esp_rom_systimer.c"], "include_dirs": ["include", "include/esp32c3", "esp32c3"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_system", "type": "LIBRARY", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_system/libesp_system.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_system/esp_err.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/crosscore_int.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/esp_ipc.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/freertos_hooks.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/int_wdt.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/panic.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/esp_system.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/startup.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/startup_funcs.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/system_time.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/stack_check.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/ubsan.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/xt_wdt.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/task_wdt/task_wdt.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/task_wdt/task_wdt_impl_timergroup.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/hw_stack_guard.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/cpu_start.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/panic_handler.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/esp_system_chip.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/image_process.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/brownout.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/arch/riscv/expression_with_stack.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/arch/riscv/panic_arch.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/arch/riscv/debug_helpers.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/arch/riscv/debug_stubs.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/soc/esp32c3/clk.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/soc/esp32c3/reset_reason.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/soc/esp32c3/system_internal.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/soc/esp32c3/cache_err_int.c", "E:/esp/v5.3.3/esp-idf/components/esp_system/port/soc/esp32c3/apb_backup_dma.c"], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_timer", "type": "LIBRARY", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_timer/libesp_timer.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_timer/src/esp_timer.c", "E:/esp/v5.3.3/esp-idf/components/esp_timer/src/esp_timer_init.c", "E:/esp/v5.3.3/esp-idf/components/esp_timer/src/ets_timer_legacy.c", "E:/esp/v5.3.3/esp-idf/components/esp_timer/src/system_time.c", "E:/esp/v5.3.3/esp-idf/components/esp_timer/src/esp_timer_impl_common.c", "E:/esp/v5.3.3/esp-idf/components/esp_timer/src/esp_timer_impl_systimer.c"], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_vfs_console", "type": "LIBRARY", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_vfs_console/libesp_vfs_console.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_vfs_console/vfs_console.c"], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_wifi", "type": "LIBRARY", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/esp_wifi/libesp_wifi.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/lib_printf.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/mesh_event.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/smartconfig.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/wifi_init.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/wifi_default.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/wifi_netif.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/wifi_default_ap.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/esp32c3/esp_adapter.c", "E:/esp/v5.3.3/esp-idf/components/esp_wifi/src/smartconfig_ack.c"], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/espcoredump", "type": "LIBRARY", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/espcoredump/libespcoredump.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_init.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_common.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_flash.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_uart.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_elf.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_binary.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_sha.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/core_dump_crc.c", "E:/esp/v5.3.3/esp-idf/components/espcoredump/src/port/riscv/core_dump_port.c"], "include_dirs": ["include", "include/port/riscv"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esptool_py", "type": "CONFIG_ONLY", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/fatfs", "type": "LIBRARY", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/fatfs/libfatfs.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/fatfs/diskio/diskio.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/diskio/diskio_rawflash.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/diskio/diskio_wl.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/src/ff.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/src/ffunicode.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/port/freertos/ffsystem.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/diskio/diskio_sdmmc.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/vfs/vfs_fat.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/vfs/vfs_fat_sdmmc.c", "E:/esp/v5.3.3/esp-idf/components/fatfs/vfs/vfs_fat_spiflash.c"], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/freertos", "type": "LIBRARY", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/freertos/libfreertos.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/freertos/heap_idf.c", "E:/esp/v5.3.3/esp-idf/components/freertos/app_startup.c", "E:/esp/v5.3.3/esp-idf/components/freertos/port_common.c", "E:/esp/v5.3.3/esp-idf/components/freertos/port_systick.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/list.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/queue.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/tasks.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/timers.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/event_groups.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/stream_buffer.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/portable/riscv/port.c", "E:/esp/v5.3.3/esp-idf/components/freertos/FreeRTOS-Kernel/portable/riscv/portasm.S", "E:/esp/v5.3.3/esp-idf/components/freertos/esp_additions/freertos_compatibility.c", "E:/esp/v5.3.3/esp-idf/components/freertos/esp_additions/idf_additions.c"], "include_dirs": ["config/include", "config/include/freertos", "config/riscv/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/riscv/include", "FreeRTOS-Kernel/portable/riscv/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/hal", "type": "LIBRARY", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/hal/libhal.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/hal/hal_utils.c", "E:/esp/v5.3.3/esp-idf/components/hal/efuse_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/esp32c3/efuse_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/wdt_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/mmu_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/cache_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/color_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_flash_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_flash_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_flash_encrypt_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/esp32c3/clk_tree_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/systimer_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/uart_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/uart_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/gpio_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/timer_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/ledc_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/ledc_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/i2c_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/i2c_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/rmt_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/twai_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/twai_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/gdma_hal_top.c", "E:/esp/v5.3.3/esp-idf/components/hal/gdma_hal_ahb_v1.c", "E:/esp/v5.3.3/esp-idf/components/hal/i2s_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/sdm_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/adc_hal_common.c", "E:/esp/v5.3.3/esp-idf/components/hal/adc_oneshot_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/adc_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/mpi_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/sha_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/aes_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/brownout_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_slave_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_slave_hal_iram.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_slave_hd_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/spi_flash_hal_gpspi.c", "E:/esp/v5.3.3/esp-idf/components/hal/hmac_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/ds_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/usb_serial_jtag_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/xt_wdt_hal.c", "E:/esp/v5.3.3/esp-idf/components/hal/esp32c3/rtc_cntl_hal.c"], "include_dirs": ["platform_port/include", "esp32c3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/heap", "type": "LIBRARY", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/heap/libheap.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/heap/heap_caps_base.c", "E:/esp/v5.3.3/esp-idf/components/heap/heap_caps.c", "E:/esp/v5.3.3/esp-idf/components/heap/heap_caps_init.c", "E:/esp/v5.3.3/esp-idf/components/heap/multi_heap.c", "E:/esp/v5.3.3/esp-idf/components/heap/tlsf/tlsf.c", "E:/esp/v5.3.3/esp-idf/components/heap/port/memory_layout_utils.c", "E:/esp/v5.3.3/esp-idf/components/heap/port/esp32c3/memory_layout.c"], "include_dirs": ["include"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/http_parser", "type": "LIBRARY", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/http_parser/libhttp_parser.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/http_parser/http_parser.c"], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/idf_test", "type": "CONFIG_ONLY", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include", "include/esp32c3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/ieee802154", "type": "CONFIG_ONLY", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/json", "type": "LIBRARY", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/json/libjson.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/json/cJSON/cJSON.c", "E:/esp/v5.3.3/esp-idf/components/json/cJSON/cJSON_Utils.c"], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/log", "type": "LIBRARY", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/log/liblog.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/log/log.c", "E:/esp/v5.3.3/esp-idf/components/log/log_buffers.c", "E:/esp/v5.3.3/esp-idf/components/log/log_freertos.c"], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/lwip", "type": "LIBRARY", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/lwip/liblwip.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/lwip/apps/sntp/sntp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/api_lib.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/api_msg.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/err.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/if_api.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/netbuf.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/netdb.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/netifapi.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/sockets.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/api/tcpip.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/apps/sntp/sntp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/apps/netbiosns/netbiosns.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/def.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/dns.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/inet_chksum.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/init.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ip.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/mem.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/memp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/netif.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/pbuf.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/raw.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/stats.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/sys.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/tcp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/tcp_in.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/tcp_out.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/timeouts.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/udp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/autoip.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/dhcp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/etharp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/icmp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/igmp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_napt.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_addr.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv4/ip4_frag.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/dhcp6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ethip6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/icmp6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/inet6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ip6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_addr.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/ip6_frag.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/mld6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/core/ipv6/nd6.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ethernet.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/bridgeif.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/bridgeif_fdb.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/slipif.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/auth.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ccp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/chap-md5.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/chap-new.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/chap_ms.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/demand.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/eap.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ecp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/eui64.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/fsm.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ipcp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ipv6cp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/lcp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/magic.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/mppe.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/multilink.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/ppp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppapi.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppcrypt.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppoe.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppol2tp.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/pppos.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/upap.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/utils.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/vj.c", "E:/esp/v5.3.3/esp-idf/components/lwip/port/hooks/tcp_isn_default.c", "E:/esp/v5.3.3/esp-idf/components/lwip/port/hooks/lwip_default_hooks.c", "E:/esp/v5.3.3/esp-idf/components/lwip/port/debug/lwip_debug.c", "E:/esp/v5.3.3/esp-idf/components/lwip/port/sockets_ext.c", "E:/esp/v5.3.3/esp-idf/components/lwip/port/freertos/sys_arch.c", "E:/esp/v5.3.3/esp-idf/components/lwip/port/esp32xx/vfs_lwip.c", "E:/esp/v5.3.3/esp-idf/components/lwip/apps/ping/esp_ping.c", "E:/esp/v5.3.3/esp-idf/components/lwip/apps/ping/ping.c", "E:/esp/v5.3.3/esp-idf/components/lwip/apps/ping/ping_sock.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/arc4.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/des.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md4.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/md5.c", "E:/esp/v5.3.3/esp-idf/components/lwip/lwip/src/netif/ppp/polarssl/sha1.c", "E:/esp/v5.3.3/esp-idf/components/lwip/apps/dhcpserver/dhcpserver.c"], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "E:/esp32_space/ble_mill_monitor_slave/main", "type": "LIBRARY", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/main/libmain.a", "sources": ["E:/esp32_space/ble_mill_monitor_slave/main/adc_ntc.c", "E:/esp32_space/ble_mill_monitor_slave/main/i2c_ms538730b.c", "E:/esp32_space/ble_mill_monitor_slave/main/wifi_ap.c", "E:/esp32_space/ble_mill_monitor_slave/main/main.c", "E:/esp32_space/ble_mill_monitor_slave/main/ble_gatt.c", "E:/esp32_space/ble_mill_monitor_slave/main/ble_gap.c", "E:/esp32_space/ble_mill_monitor_slave/main/fs_mount.c", "E:/esp32_space/ble_mill_monitor_slave/main/file_server.c", "E:/esp32_space/ble_mill_monitor_slave/build/favicon.ico.S", "E:/esp32_space/ble_mill_monitor_slave/build/upload_script.html.S"], "include_dirs": ["."]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/mbedtls", "type": "LIBRARY", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/mbedtls/libmbedtls.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/mbedtls/esp_crt_bundle/esp_crt_bundle.c", "E:/esp32_space/ble_mill_monitor_slave/build/x509_crt_bundle.S"], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/mqtt", "type": "LIBRARY", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/mqtt/libmqtt.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/mqtt_client.c", "E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_msg.c", "E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/lib/mqtt_outbox.c", "E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/lib/platform_esp32_idf.c"], "include_dirs": ["E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/newlib", "type": "LIBRARY", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/newlib/libnewlib.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/newlib/abort.c", "E:/esp/v5.3.3/esp-idf/components/newlib/assert.c", "E:/esp/v5.3.3/esp-idf/components/newlib/heap.c", "E:/esp/v5.3.3/esp-idf/components/newlib/locks.c", "E:/esp/v5.3.3/esp-idf/components/newlib/poll.c", "E:/esp/v5.3.3/esp-idf/components/newlib/pthread.c", "E:/esp/v5.3.3/esp-idf/components/newlib/random.c", "E:/esp/v5.3.3/esp-idf/components/newlib/getentropy.c", "E:/esp/v5.3.3/esp-idf/components/newlib/reent_init.c", "E:/esp/v5.3.3/esp-idf/components/newlib/newlib_init.c", "E:/esp/v5.3.3/esp-idf/components/newlib/syscalls.c", "E:/esp/v5.3.3/esp-idf/components/newlib/termios.c", "E:/esp/v5.3.3/esp-idf/components/newlib/stdatomic.c", "E:/esp/v5.3.3/esp-idf/components/newlib/time.c", "E:/esp/v5.3.3/esp-idf/components/newlib/sysconf.c", "E:/esp/v5.3.3/esp-idf/components/newlib/realpath.c", "E:/esp/v5.3.3/esp-idf/components/newlib/scandir.c", "E:/esp/v5.3.3/esp-idf/components/newlib/port/esp_time_impl.c"], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/nvs_flash", "type": "LIBRARY", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/nvs_flash/libnvs_flash.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_api.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_cxx_api.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_item_hash_list.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_page.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_pagemanager.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_storage.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_handle_simple.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_handle_locked.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_partition.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_partition_lookup.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_partition_manager.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_types.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_platform.cpp", "E:/esp/v5.3.3/esp-idf/components/nvs_flash/src/nvs_encrypted_partition.cpp"], "include_dirs": ["include", "../spi_flash/include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/nvs_sec_provider", "type": "LIBRARY", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/nvs_sec_provider/libnvs_sec_provider.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/nvs_sec_provider/nvs_sec_provider.c"], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/openthread", "type": "CONFIG_ONLY", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/partition_table", "type": "CONFIG_ONLY", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/protobuf-c", "type": "LIBRARY", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/protobuf-c/libprotobuf-c.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/protobuf-c/protobuf-c/protobuf-c/protobuf-c.c"], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/protocomm", "type": "LIBRARY", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/protocomm/libprotocomm.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/protocomm/src/common/protocomm.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/proto-c/constants.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/proto-c/sec0.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/proto-c/sec1.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/proto-c/sec2.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/proto-c/session.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/transports/protocomm_console.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/transports/protocomm_httpd.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/security/security0.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/security/security1.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/security/security2.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/crypto/srp6a/esp_srp_mpi.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/simple_ble/simple_ble.c", "E:/esp/v5.3.3/esp-idf/components/protocomm/src/transports/protocomm_ble.c"], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/pthread", "type": "LIBRARY", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/pthread/libpthread.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/pthread/pthread.c", "E:/esp/v5.3.3/esp-idf/components/pthread/pthread_cond_var.c", "E:/esp/v5.3.3/esp-idf/components/pthread/pthread_local_storage.c", "E:/esp/v5.3.3/esp-idf/components/pthread/pthread_rwlock.c", "E:/esp/v5.3.3/esp-idf/components/pthread/pthread_semaphore.c"], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/riscv", "type": "LIBRARY", "lib": "__idf_riscv", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/riscv/libriscv.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/riscv/instruction_decode.c", "E:/esp/v5.3.3/esp-idf/components/riscv/interrupt.c", "E:/esp/v5.3.3/esp-idf/components/riscv/vectors.S", "E:/esp/v5.3.3/esp-idf/components/riscv/interrupt_intc.c", "E:/esp/v5.3.3/esp-idf/components/riscv/vectors_intc.S"], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/sdmmc", "type": "LIBRARY", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/sdmmc/libsdmmc.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/sdmmc/sdmmc_cmd.c", "E:/esp/v5.3.3/esp-idf/components/sdmmc/sdmmc_common.c", "E:/esp/v5.3.3/esp-idf/components/sdmmc/sdmmc_init.c", "E:/esp/v5.3.3/esp-idf/components/sdmmc/sdmmc_io.c", "E:/esp/v5.3.3/esp-idf/components/sdmmc/sdmmc_mmc.c", "E:/esp/v5.3.3/esp-idf/components/sdmmc/sdmmc_sd.c", "E:/esp/v5.3.3/esp-idf/components/sdmmc/sd_pwr_ctrl/sd_pwr_ctrl.c"], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/soc", "type": "LIBRARY", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/soc/libsoc.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/soc/lldesc.c", "E:/esp/v5.3.3/esp-idf/components/soc/dport_access_common.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/interrupts.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/gpio_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/uart_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/adc_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/dedic_gpio_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/gdma_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/spi_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/ledc_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/rmt_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/sdm_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/i2s_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/i2c_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/temperature_sensor_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/timer_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/mpi_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/twai_periph.c", "E:/esp/v5.3.3/esp-idf/components/soc/esp32c3/wdt_periph.c"], "include_dirs": ["include", "esp32c3", "esp32c3/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/spi_flash", "type": "LIBRARY", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/spi_flash/libspi_flash.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/spi_flash/flash_brownout_hook.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_drivers.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_generic.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_issi.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_mxic.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_gd.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_winbond.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_boya.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_mxic_opi.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_chip_th.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/memspi_host_driver.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/cache_utils.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/flash_mmap.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/flash_ops.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_wrap.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/esp_flash_api.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/esp_flash_spi_init.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_os_func_app.c", "E:/esp/v5.3.3/esp-idf/components/spi_flash/spi_flash_os_func_noos.c"], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/spiffs", "type": "LIBRARY", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/spiffs/libspiffs.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/spiffs/spiffs_api.c", "E:/esp/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_cache.c", "E:/esp/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_check.c", "E:/esp/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_gc.c", "E:/esp/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_hydrogen.c", "E:/esp/v5.3.3/esp-idf/components/spiffs/spiffs/src/spiffs_nucleus.c", "E:/esp/v5.3.3/esp-idf/components/spiffs/esp_spiffs.c"], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/tcp_transport", "type": "LIBRARY", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/tcp_transport/libtcp_transport.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/tcp_transport/transport.c", "E:/esp/v5.3.3/esp-idf/components/tcp_transport/transport_ssl.c", "E:/esp/v5.3.3/esp-idf/components/tcp_transport/transport_internal.c", "E:/esp/v5.3.3/esp-idf/components/tcp_transport/transport_socks_proxy.c", "E:/esp/v5.3.3/esp-idf/components/tcp_transport/transport_ws.c"], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/ulp", "type": "CONFIG_ONLY", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/unity", "type": "LIBRARY", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/unity/libunity.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/unity/unity/src/unity.c", "E:/esp/v5.3.3/esp-idf/components/unity/unity_compat.c", "E:/esp/v5.3.3/esp-idf/components/unity/unity_runner.c", "E:/esp/v5.3.3/esp-idf/components/unity/unity_utils_freertos.c", "E:/esp/v5.3.3/esp-idf/components/unity/unity_utils_cache.c", "E:/esp/v5.3.3/esp-idf/components/unity/unity_utils_memory.c", "E:/esp/v5.3.3/esp-idf/components/unity/unity_port_esp32.c", "E:/esp/v5.3.3/esp-idf/components/unity/port/esp/unity_utils_memory_esp.c"], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/usb", "type": "CONFIG_ONLY", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "file": "", "sources": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/vfs", "type": "LIBRARY", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/vfs/libvfs.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/vfs/vfs.c", "E:/esp/v5.3.3/esp-idf/components/vfs/vfs_eventfd.c", "E:/esp/v5.3.3/esp-idf/components/vfs/vfs_semihost.c"], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/wear_levelling", "type": "LIBRARY", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/wear_levelling/libwear_levelling.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/wear_levelling/Partition.cpp", "E:/esp/v5.3.3/esp-idf/components/wear_levelling/SPI_Flash.cpp", "E:/esp/v5.3.3/esp-idf/components/wear_levelling/WL_Ext_Perf.cpp", "E:/esp/v5.3.3/esp-idf/components/wear_levelling/WL_Ext_Safe.cpp", "E:/esp/v5.3.3/esp-idf/components/wear_levelling/WL_Flash.cpp", "E:/esp/v5.3.3/esp-idf/components/wear_levelling/crc32.cpp", "E:/esp/v5.3.3/esp-idf/components/wear_levelling/wear_levelling.cpp"], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning", "type": "LIBRARY", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/wifi_provisioning/libwifi_provisioning.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/wifi_config.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/wifi_scan.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/wifi_ctrl.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/manager.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/handlers.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/scheme_console.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_config.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_scan.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_ctrl.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/proto-c/wifi_constants.pb-c.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/scheme_softap.c", "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning/src/scheme_ble.c"], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant", "type": "LIBRARY", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "file": "E:/esp32_space/ble_mill_monitor_slave/build/esp-idf/wpa_supplicant/libwpa_supplicant.a", "sources": ["E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/port/os_xtensa.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/port/eloop.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/ap_config.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/ieee802_1x.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/wpa_auth.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/wpa_auth_ie.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/pmksa_cache_auth.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/sta_info.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/ieee802_11.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/ap/comeback_token.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/common/sae.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/common/dragonfly.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/common/wpa_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/bitfield.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-siv.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha256-kdf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/ccmp.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-gcm.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/crypto_ops.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/dh_group5.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/dh_groups.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/ms_funcs.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha1-tlsprf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha256-tlsprf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha384-tlsprf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha256-prf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha1-prf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha384-prf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/md4-internal.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/sha1-tprf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_common/eap_wsc_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/common/ieee802_11_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/chap.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_mschapv2.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_peap_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_tls_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_ttls.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/mschapv2.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/eap_peer/eap_fast_pac.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/rsn_supp/pmksa_cache.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/rsn_supp/wpa_ie.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/base64.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/ext_password.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/uuid.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/wpabuf.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/wpa_debug.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/utils/json.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_attr_build.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_attr_parse.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_attr_process.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_dev_attr.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/wps/wps_enrollee.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/common/sae_pk.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_eap_client.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa2_api_port.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa_main.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpas_glue.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_common.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wps.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_wpa3.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_owe.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/esp_hostap.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/tls_mbedtls.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/fastpbkdf2.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-bignum.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-rsa.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/esp_supplicant/src/crypto/crypto_mbedtls-ec.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/rc4.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/des-internal.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-wrap.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-unwrap.c", "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant/src/crypto/aes-ccm.c"], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}}, "all_component_info": {"app_trace": {"alias": "idf::app_trace", "target": "___idf_app_trace", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/app_trace", "lib": "__idf_app_trace", "reqs": ["esp_timer"], "priv_reqs": ["esp_driver_gptimer", "esp_driver_gpio", "esp_driver_uart"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "app_update": {"alias": "idf::app_update", "target": "___idf_app_update", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/app_update", "lib": "__idf_app_update", "reqs": ["partition_table", "bootloader_support", "esp_app_format", "esp_bootloader_format", "esp_partition"], "priv_reqs": ["esptool_py", "efuse", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "bootloader": {"alias": "idf::bootloader", "target": "___idf_bootloader", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/bootloader", "lib": "__idf_bootloader", "reqs": [], "priv_reqs": ["partition_table", "esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "bootloader_support": {"alias": "idf::bootloader_support", "target": "___idf_bootloader_support", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/bootloader_support", "lib": "__idf_bootloader_support", "reqs": ["soc"], "priv_reqs": ["spi_flash", "mbedtls", "efuse", "heap", "esp_bootloader_format", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "bootloader_flash/include"]}, "bt": {"alias": "idf::bt", "target": "___idf_bt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/bt", "lib": "__idf_bt", "reqs": ["esp_timer", "esp_wifi"], "priv_reqs": ["nvs_flash", "soc", "esp_pm", "esp_phy", "esp_coex", "mbedtls", "esp_driver_uart", "vfs", "esp_ringbuf", "esp_driver_spi", "esp_driver_gpio", "esp_gdbstub"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/esp32c3/include", "common/osi/include", "common/api/include/api", "common/btc/profile/esp/blufi/include", "common/btc/profile/esp/include", "common/hci_log/include", "common/ble_log/include", "host/bluedroid/api/include/api", "porting/ext/tinycrypt/include"]}, "cmock": {"alias": "idf::cmock", "target": "___idf_cmock", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/cmock", "lib": "__idf_cmock", "reqs": ["unity"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["CMock/src"]}, "console": {"alias": "idf::console", "target": "___idf_console", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/console", "lib": "__idf_console", "reqs": ["vfs", "esp_vfs_console"], "priv_reqs": ["esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/esp/v5.3.3/esp-idf/components/console"]}, "cxx": {"alias": "idf::cxx", "target": "___idf_cxx", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/cxx", "lib": "__idf_cxx", "reqs": [], "priv_reqs": ["pthread", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "driver": {"alias": "idf::driver", "target": "___idf_driver", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/driver", "lib": "__idf_driver", "reqs": ["esp_pm", "esp_ringbuf", "freertos", "soc", "hal", "esp_hw_support", "esp_driver_gpio", "esp_driver_pcnt", "esp_driver_gptimer", "esp_driver_spi", "esp_driver_mcpwm", "esp_driver_ana_cmpr", "esp_driver_i2s", "esp_driver_sdmmc", "esp_driver_sdspi", "esp_driver_sdio", "esp_driver_dac", "esp_driver_rmt", "esp_driver_tsens", "esp_driver_sdm", "esp_driver_i2c", "esp_driver_uart", "esp_driver_ledc", "esp_driver_parlio", "esp_driver_usb_serial_jtag"], "priv_reqs": ["efuse", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["deprecated", "i2c/include", "touch_sensor/include", "twai/include"]}, "efuse": {"alias": "idf::efuse", "target": "___idf_efuse", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/efuse", "lib": "__idf_efuse", "reqs": [], "priv_reqs": ["bootloader_support", "soc", "spi_flash", "esp_system", "esp_partition", "esp_app_format"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32c3/include"]}, "esp-tls": {"alias": "idf::esp-tls", "target": "___idf_esp-tls", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp-tls", "lib": "__idf_esp-tls", "reqs": ["mbedtls"], "priv_reqs": ["http_parser", "esp_timer", "lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/esp/v5.3.3/esp-idf/components/esp-tls", "esp-tls-crypto"]}, "esp_adc": {"alias": "idf::esp_adc", "target": "___idf_esp_adc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_adc", "lib": "__idf_esp_adc", "reqs": [], "priv_reqs": ["driver", "esp_driver_gpio", "efuse", "esp_pm", "esp_ringbuf", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface", "esp32c3/include", "deprecated/include"]}, "esp_app_format": {"alias": "idf::esp_app_format", "target": "___idf_esp_app_format", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_app_format", "lib": "__idf_esp_app_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_bootloader_format": {"alias": "idf::esp_bootloader_format", "target": "___idf_esp_bootloader_format", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_bootloader_format", "lib": "__idf_esp_bootloader_format", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_coex": {"alias": "idf::esp_coex", "target": "___idf_esp_coex", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_coex", "lib": "__idf_esp_coex", "reqs": [], "priv_reqs": ["esp_timer", "driver", "esp_event"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_common": {"alias": "idf::esp_common", "target": "___idf_esp_common", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_common", "lib": "__idf_esp_common", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ana_cmpr": {"alias": "idf::esp_driver_ana_cmpr", "target": "___idf_esp_driver_ana_cmpr", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_ana_cmpr", "lib": "__idf_esp_driver_ana_cmpr", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_cam": {"alias": "idf::esp_driver_cam", "target": "___idf_esp_driver_cam", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_cam", "lib": "__idf_esp_driver_cam", "reqs": ["esp_driver_isp", "esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_driver_dac": {"alias": "idf::esp_driver_dac", "target": "___idf_esp_driver_dac", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_dac", "lib": "__idf_esp_driver_dac", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["./include"]}, "esp_driver_gpio": {"alias": "idf::esp_driver_gpio", "target": "___idf_esp_driver_gpio", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_gpio", "lib": "__idf_esp_driver_gpio", "reqs": [], "priv_reqs": ["esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_gptimer": {"alias": "idf::esp_driver_gptimer", "target": "___idf_esp_driver_gptimer", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_gptimer", "lib": "__idf_esp_driver_gptimer", "reqs": ["esp_pm"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2c": {"alias": "idf::esp_driver_i2c", "target": "___idf_esp_driver_i2c", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2c", "lib": "__idf_esp_driver_i2c", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_i2s": {"alias": "idf::esp_driver_i2s", "target": "___idf_esp_driver_i2s", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_i2s", "lib": "__idf_esp_driver_i2s", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_isp": {"alias": "idf::esp_driver_isp", "target": "___idf_esp_driver_isp", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_isp", "lib": "__idf_esp_driver_isp", "reqs": ["esp_mm"], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_jpeg": {"alias": "idf::esp_driver_jpeg", "target": "___idf_esp_driver_jpeg", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_jpeg", "lib": "__idf_esp_driver_jpeg", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ledc": {"alias": "idf::esp_driver_ledc", "target": "___idf_esp_driver_ledc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_ledc", "lib": "__idf_esp_driver_ledc", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_mcpwm": {"alias": "idf::esp_driver_mcpwm", "target": "___idf_esp_driver_mcpwm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_mcpwm", "lib": "__idf_esp_driver_mcpwm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_parlio": {"alias": "idf::esp_driver_parlio", "target": "___idf_esp_driver_parlio", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_parlio", "lib": "__idf_esp_driver_parlio", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_pcnt": {"alias": "idf::esp_driver_pcnt", "target": "___idf_esp_driver_pcnt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_pcnt", "lib": "__idf_esp_driver_pcnt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_ppa": {"alias": "idf::esp_driver_ppa", "target": "___idf_esp_driver_ppa", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_ppa", "lib": "__idf_esp_driver_ppa", "reqs": [], "priv_reqs": ["esp_mm", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_rmt": {"alias": "idf::esp_driver_rmt", "target": "___idf_esp_driver_rmt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_rmt", "lib": "__idf_esp_driver_rmt", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdio": {"alias": "idf::esp_driver_sdio", "target": "___idf_esp_driver_sdio", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdio", "lib": "__idf_esp_driver_sdio", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdm": {"alias": "idf::esp_driver_sdm", "target": "___idf_esp_driver_sdm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdm", "lib": "__idf_esp_driver_sdm", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdmmc": {"alias": "idf::esp_driver_sdmmc", "target": "___idf_esp_driver_sdmmc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdmmc", "lib": "__idf_esp_driver_sdmmc", "reqs": ["sdmmc", "esp_driver_gpio"], "priv_reqs": ["esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_sdspi": {"alias": "idf::esp_driver_sdspi", "target": "___idf_esp_driver_sdspi", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_sdspi", "lib": "__idf_esp_driver_sdspi", "reqs": ["sdmmc", "esp_driver_spi", "esp_driver_gpio"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_spi": {"alias": "idf::esp_driver_spi", "target": "___idf_esp_driver_spi", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_spi", "lib": "__idf_esp_driver_spi", "reqs": ["esp_pm"], "priv_reqs": ["esp_timer", "esp_mm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_touch_sens": {"alias": "idf::esp_driver_touch_sens", "target": "___idf_esp_driver_touch_sens", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_touch_sens", "lib": "__idf_esp_driver_touch_sens", "reqs": [], "priv_reqs": ["esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_driver_tsens": {"alias": "idf::esp_driver_tsens", "target": "___idf_esp_driver_tsens", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_tsens", "lib": "__idf_esp_driver_tsens", "reqs": [], "priv_reqs": ["efuse"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_uart": {"alias": "idf::esp_driver_uart", "target": "___idf_esp_driver_uart", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_uart", "lib": "__idf_esp_driver_uart", "reqs": [], "priv_reqs": ["esp_pm", "esp_driver_gpio", "esp_ringbuf"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_driver_usb_serial_jtag": {"alias": "idf::esp_driver_usb_serial_jtag", "target": "___idf_esp_driver_usb_serial_jtag", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_driver_usb_serial_jtag", "lib": "__idf_esp_driver_usb_serial_jtag", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_ringbuf", "esp_pm", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_eth": {"alias": "idf::esp_eth", "target": "___idf_esp_eth", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_eth", "lib": "__idf_esp_eth", "reqs": ["esp_event"], "priv_reqs": ["log", "esp_timer", "esp_driver_spi", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_event": {"alias": "idf::esp_event", "target": "___idf_esp_event", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_event", "lib": "__idf_esp_event", "reqs": ["log", "esp_common", "freertos"], "priv_reqs": ["esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_gdbstub": {"alias": "idf::esp_gdbstub", "target": "___idf_esp_gdbstub", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_gdbstub", "lib": "__idf_esp_gdbstub", "reqs": ["freertos"], "priv_reqs": ["soc", "esp_rom", "esp_system"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hid": {"alias": "idf::esp_hid", "target": "___idf_esp_hid", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_hid", "lib": "__idf_esp_hid", "reqs": ["esp_event", "bt"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_client": {"alias": "idf::esp_http_client", "target": "___idf_esp_http_client", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_http_client", "lib": "__idf_esp_http_client", "reqs": ["lwip", "esp_event"], "priv_reqs": ["tcp_transport", "http_parser"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_http_server": {"alias": "idf::esp_http_server", "target": "___idf_esp_http_server", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_http_server", "lib": "__idf_esp_http_server", "reqs": ["http_parser", "esp_event"], "priv_reqs": ["mbedtls", "lwip", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_ota": {"alias": "idf::esp_https_ota", "target": "___idf_esp_https_ota", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_https_ota", "lib": "__idf_esp_https_ota", "reqs": ["esp_http_client", "bootloader_support", "esp_app_format", "esp_event"], "priv_reqs": ["log", "app_update"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_https_server": {"alias": "idf::esp_https_server", "target": "___idf_esp_https_server", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_https_server", "lib": "__idf_esp_https_server", "reqs": ["esp_http_server", "esp-tls", "esp_event"], "priv_reqs": ["lwip"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_hw_support": {"alias": "idf::esp_hw_support", "target": "___idf_esp_hw_support", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_hw_support", "lib": "__idf_esp_hw_support", "reqs": ["soc"], "priv_reqs": ["efuse", "spi_flash", "bootloader_support", "esp_driver_gpio", "esp_timer", "esp_pm", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/soc", "include/soc/esp32c3", "dma/include", "ldo/include"]}, "esp_lcd": {"alias": "idf::esp_lcd", "target": "___idf_esp_lcd", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_lcd", "lib": "__idf_esp_lcd", "reqs": ["driver", "esp_driver_gpio", "esp_driver_i2c", "esp_driver_spi"], "priv_reqs": ["esp_mm", "esp_psram", "esp_pm", "esp_driver_i2s"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "interface"]}, "esp_local_ctrl": {"alias": "idf::esp_local_ctrl", "target": "___idf_esp_local_ctrl", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_local_ctrl", "lib": "__idf_esp_local_ctrl", "reqs": ["protocomm", "esp_https_server"], "priv_reqs": ["protobuf-c"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_mm": {"alias": "idf::esp_mm", "target": "___idf_esp_mm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_mm", "lib": "__idf_esp_mm", "reqs": [], "priv_reqs": ["heap", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif": {"alias": "idf::esp_netif", "target": "___idf_esp_netif", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_netif", "lib": "__idf_esp_netif", "reqs": ["esp_event"], "priv_reqs": ["esp_netif_stack"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_netif_stack": {"alias": "idf::esp_netif_stack", "target": "___idf_esp_netif_stack", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_netif_stack", "lib": "__idf_esp_netif_stack", "reqs": ["lwip"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "esp_partition": {"alias": "idf::esp_partition", "target": "___idf_esp_partition", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_partition", "lib": "__idf_esp_partition", "reqs": [], "priv_reqs": ["esp_system", "bootloader_support", "spi_flash", "app_update", "partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_phy": {"alias": "idf::esp_phy", "target": "___idf_esp_phy", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_phy", "lib": "__idf_esp_phy", "reqs": [], "priv_reqs": ["nvs_flash", "driver", "efuse", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32c3/include"]}, "esp_pm": {"alias": "idf::esp_pm", "target": "___idf_esp_pm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_pm", "lib": "__idf_esp_pm", "reqs": [], "priv_reqs": ["esp_system", "esp_driver_gpio", "esp_timer"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_psram": {"alias": "idf::esp_psram", "target": "___idf_esp_psram", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_psram", "lib": "__idf_esp_psram", "reqs": [], "priv_reqs": ["heap", "spi_flash", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_ringbuf": {"alias": "idf::esp_ringbuf", "target": "___idf_esp_ringbuf", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_ringbuf", "lib": "__idf_esp_ringbuf", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_rom": {"alias": "idf::esp_rom", "target": "___idf_esp_rom", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_rom", "lib": "__idf_esp_rom", "reqs": [], "priv_reqs": ["soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32c3", "esp32c3"]}, "esp_system": {"alias": "idf::esp_system", "target": "___idf_esp_system", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_system", "lib": "__idf_esp_system", "reqs": [], "priv_reqs": ["spi_flash", "esp_timer", "esp_mm", "bootloader_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_timer": {"alias": "idf::esp_timer", "target": "___idf_esp_timer", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_timer", "lib": "__idf_esp_timer", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_vfs_console": {"alias": "idf::esp_vfs_console", "target": "___idf_esp_vfs_console", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_vfs_console", "lib": "__idf_esp_vfs_console", "reqs": [], "priv_reqs": ["vfs", "esp_driver_uart", "esp_driver_usb_serial_jtag"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "esp_wifi": {"alias": "idf::esp_wifi", "target": "___idf_esp_wifi", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esp_wifi", "lib": "__idf_esp_wifi", "reqs": ["esp_event", "esp_phy", "esp_netif"], "priv_reqs": ["driver", "esptool_py", "esp_pm", "esp_timer", "nvs_flash", "wpa_supplicant", "hal", "lwip", "esp_coex"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/local", "wifi_apps/include", "wifi_apps/nan_app/include"]}, "espcoredump": {"alias": "idf::espcoredump", "target": "___idf_espcoredump", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/espcoredump", "lib": "__idf_espcoredump", "reqs": [], "priv_reqs": ["esp_partition", "spi_flash", "bootloader_support", "mbedtls", "esp_rom", "soc", "esp_system", "esp_driver_gpio", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/port/riscv"]}, "esptool_py": {"alias": "idf::esptool_py", "target": "___idf_esptool_py", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/esptool_py", "lib": "__idf_esptool_py", "reqs": ["bootloader"], "priv_reqs": ["partition_table"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "fatfs": {"alias": "idf::fatfs", "target": "___idf_fatfs", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/fatfs", "lib": "__idf_fatfs", "reqs": ["wear_levelling", "sdmmc", "esp_driver_sdmmc", "esp_driver_sdspi"], "priv_reqs": ["vfs", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["diskio", "src", "vfs"]}, "freertos": {"alias": "idf::freertos", "target": "___idf_freertos", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/freertos", "lib": "__idf_freertos", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["config/include", "config/include/freertos", "config/riscv/include", "FreeRTOS-Kernel/include", "FreeRTOS-Kernel/portable/riscv/include", "FreeRTOS-Kernel/portable/riscv/include/freertos", "esp_additions/include"]}, "hal": {"alias": "idf::hal", "target": "___idf_hal", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/hal", "lib": "__idf_hal", "reqs": ["soc", "esp_rom"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_port/include", "esp32c3/include", "include"]}, "heap": {"alias": "idf::heap", "target": "___idf_heap", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/heap", "lib": "__idf_heap", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "http_parser": {"alias": "idf::http_parser", "target": "___idf_http_parser", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/http_parser", "lib": "__idf_http_parser", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}, "idf_test": {"alias": "idf::idf_test", "target": "___idf_idf_test", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/idf_test", "lib": "__idf_idf_test", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/esp32c3"]}, "ieee802154": {"alias": "idf::ieee802154", "target": "___idf_ieee802154", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/ieee802154", "lib": "__idf_ieee802154", "reqs": ["esp_coex"], "priv_reqs": ["esp_phy", "driver", "esp_timer", "soc", "hal"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "json": {"alias": "idf::json", "target": "___idf_json", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/json", "lib": "__idf_json", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "linux": {"alias": "idf::linux", "target": "___idf_linux", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/linux", "lib": "__idf_linux", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["cJSON"]}, "log": {"alias": "idf::log", "target": "___idf_log", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/log", "lib": "__idf_log", "reqs": [], "priv_reqs": ["soc", "hal", "esp_hw_support"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "lwip": {"alias": "idf::lwip", "target": "___idf_lwip", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/lwip", "lib": "__idf_lwip", "reqs": [], "priv_reqs": ["vfs"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "include/apps", "include/apps/sntp", "lwip/src/include", "port/include", "port/freertos/include/", "port/esp32xx/include", "port/esp32xx/include/arch", "port/esp32xx/include/sys"]}, "mbedtls": {"alias": "idf::mbedtls", "target": "___idf_mbedtls", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/mbedtls", "lib": "__idf_mbedtls", "reqs": [], "priv_reqs": ["soc", "esp_hw_support", "esp_pm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["port/include", "mbedtls/include", "mbedtls/library", "esp_crt_bundle/include"]}, "mqtt": {"alias": "idf::mqtt", "target": "___idf_mqtt", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/mqtt", "lib": "__idf_mqtt", "reqs": ["esp_event", "tcp_transport"], "priv_reqs": ["esp_timer", "http_parser", "esp_hw_support", "heap"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["E:/esp/v5.3.3/esp-idf/components/mqtt/esp-mqtt/include"]}, "newlib": {"alias": "idf::newlib", "target": "___idf_newlib", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/newlib", "lib": "__idf_newlib", "reqs": [], "priv_reqs": ["soc", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["platform_include"]}, "nvs_flash": {"alias": "idf::nvs_flash", "target": "___idf_nvs_flash", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/nvs_flash", "lib": "__idf_nvs_flash", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash", "newlib"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "../spi_flash/include"]}, "nvs_sec_provider": {"alias": "idf::nvs_sec_provider", "target": "___idf_nvs_sec_provider", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/nvs_sec_provider", "lib": "__idf_nvs_sec_provider", "reqs": [], "priv_reqs": ["bootloader_support", "efuse", "esp_partition", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "openthread": {"alias": "idf::openthread", "target": "___idf_openthread", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/openthread", "lib": "__idf_openthread", "reqs": ["esp_netif", "lwip", "esp_driver_uart", "driver"], "priv_reqs": ["console", "esp_coex", "esp_event", "esp_partition", "esp_timer", "ieee802154", "mbedtls", "nvs_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "partition_table": {"alias": "idf::partition_table", "target": "___idf_partition_table", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/partition_table", "lib": "__idf_partition_table", "reqs": [], "priv_reqs": ["esptool_py"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "perfmon": {"alias": "idf::perfmon", "target": "___idf_perfmon", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/perfmon", "lib": "__idf_perfmon", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "protobuf-c": {"alias": "idf::protobuf-c", "target": "___idf_protobuf-c", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/protobuf-c", "lib": "__idf_protobuf-c", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["protobuf-c"]}, "protocomm": {"alias": "idf::protocomm", "target": "___idf_protocomm", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/protocomm", "lib": "__idf_protocomm", "reqs": ["bt"], "priv_reqs": ["protobuf-c", "mbedtls", "console", "esp_http_server", "driver"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include/common", "include/security", "include/transports", "include/crypto/srp6a", "proto-c"]}, "pthread": {"alias": "idf::pthread", "target": "___idf_pthread", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/pthread", "lib": "__idf_pthread", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "riscv": {"alias": "idf::riscv", "target": "___idf_riscv", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/riscv", "lib": "__idf_riscv", "reqs": [], "priv_reqs": ["soc"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "sdmmc": {"alias": "idf::sdmmc", "target": "___idf_sdmmc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/sdmmc", "lib": "__idf_sdmmc", "reqs": [], "priv_reqs": ["soc", "esp_timer", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "soc": {"alias": "idf::soc", "target": "___idf_soc", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/soc", "lib": "__idf_soc", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "esp32c3", "esp32c3/include"]}, "spi_flash": {"alias": "idf::spi_flash", "target": "___idf_spi_flash", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/spi_flash", "lib": "__idf_spi_flash", "reqs": ["hal"], "priv_reqs": ["bootloader_support", "app_update", "soc", "esp_mm", "esp_driver_gpio"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "spiffs": {"alias": "idf::spiffs", "target": "___idf_spiffs", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/spiffs", "lib": "__idf_spiffs", "reqs": ["esp_partition"], "priv_reqs": ["bootloader_support", "esptool_py", "vfs", "spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "tcp_transport": {"alias": "idf::tcp_transport", "target": "___idf_tcp_transport", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/tcp_transport", "lib": "__idf_tcp_transport", "reqs": ["esp-tls", "lwip", "esp_timer"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "touch_element": {"alias": "idf::touch_element", "target": "___idf_touch_element", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/touch_element", "lib": "__idf_touch_element", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "ulp": {"alias": "idf::ulp", "target": "___idf_ulp", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/ulp", "lib": "__idf_ulp", "reqs": ["driver", "esp_adc"], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "unity": {"alias": "idf::unity", "target": "___idf_unity", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/unity", "lib": "__idf_unity", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "unity/src"]}, "usb": {"alias": "idf::usb", "target": "___idf_usb", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/usb", "lib": "__idf_usb", "reqs": [], "priv_reqs": ["esp_driver_gpio", "esp_mm"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": []}, "vfs": {"alias": "idf::vfs", "target": "___idf_vfs", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/vfs", "lib": "__idf_vfs", "reqs": [], "priv_reqs": ["esp_timer", "esp_driver_uart", "esp_driver_usb_serial_jtag", "esp_vfs_console"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wear_levelling": {"alias": "idf::wear_levelling", "target": "___idf_wear_levelling", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/wear_levelling", "lib": "__idf_wear_levelling", "reqs": ["esp_partition"], "priv_reqs": ["spi_flash"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wifi_provisioning": {"alias": "idf::wifi_provisioning", "target": "___idf_wifi_provisioning", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/wifi_provisioning", "lib": "__idf_wifi_provisioning", "reqs": ["lwip", "protocomm"], "priv_reqs": ["protobuf-c", "bt", "json", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include"]}, "wpa_supplicant": {"alias": "idf::wpa_supplicant", "target": "___idf_wpa_supplicant", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/wpa_supplicant", "lib": "__idf_wpa_supplicant", "reqs": [], "priv_reqs": ["mbedtls", "esp_timer", "esp_wifi"], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "xtensa": {"alias": "idf::xtensa", "target": "___idf_xtensa", "prefix": "idf", "dir": "E:/esp/v5.3.3/esp-idf/components/xtensa", "lib": "__idf_xtensa", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["include", "port/include", "esp_supplicant/include"]}, "main": {"alias": "idf::main", "target": "___idf_main", "prefix": "idf", "dir": "E:/esp32_space/ble_mill_monitor_slave/main", "lib": "__idf_main", "reqs": [], "priv_reqs": [], "managed_reqs": [], "managed_priv_reqs": [], "include_dirs": ["."]}}, "debug_prefix_map_gdbinit": "", "gdbinit_files": {"01_symbols": "E:/esp32_space/ble_mill_monitor_slave/build/gdbinit/symbols", "02_prefix_map": "E:/esp32_space/ble_mill_monitor_slave/build/gdbinit/prefix_map", "03_py_extensions": "E:/esp32_space/ble_mill_monitor_slave/build/gdbinit/py_extensions", "04_connect": "E:/esp32_space/ble_mill_monitor_slave/build/gdbinit/connect"}, "debug_arguments_openocd": "-f board/esp32c3-builtin.cfg"}