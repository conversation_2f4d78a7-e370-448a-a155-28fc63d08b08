# ninja log v6
58	247	7753259824841012	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_crc.c.obj	e15820dea774621b
74	288	7753259824991014	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_sys.c.obj	af432d4b589ee1b5
16	392	7753259824420875	esp-idf/log/CMakeFiles/__idf_log.dir/log.c.obj	ba5714f7c4cdf445
101	408	7753259825261066	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_spiflash.c.obj	59e7541cb455c098
45	422	7753259824700968	esp-idf/log/CMakeFiles/__idf_log.dir/log_noos.c.obj	ece775a80cae7ca8
116	439	7753259825421102	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_efuse.c.obj	af1371b6488c4d52
88	457	7753259825131038	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_uart.c.obj	a86d33291ee8e0f6
132	505	7753259825571131	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_gpio.c.obj	f8c0c43f38767e10
31	524	7753259824560904	esp-idf/log/CMakeFiles/__idf_log.dir/log_buffers.c.obj	1c5e934baaf54c2a
206	537	7753259826311298	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/esp_memory_utils.c.obj	88ccfd9567c65b09
149	554	7753259825741183	esp-idf/esp_rom/CMakeFiles/__idf_esp_rom.dir/patches/esp_rom_systimer.c.obj	84131d17d4ce8051
164	572	7753259825891209	esp-idf/esp_common/CMakeFiles/__idf_esp_common.dir/src/esp_err_to_name.c.obj	654c0bde84e538c2
193	588	7753259826181273	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/esp_cpu_intr.c.obj	58f1bd03d1f0a630
179	605	7753259826041240	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/cpu.c.obj	6c3539b9a6b2c090
250	623	7753259826751400	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/cpu_region_protect.c.obj	47e59a816f7625a3
288	827	7753259827131484	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk_init.c.obj	e3713d4c02030940
505	938	7753259829301977	esp-idf/esp_system/CMakeFiles/__idf_esp_system.dir/esp_err.c.obj	44b23d4f400bd499
457	1033	7753259828821869	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/chip_info.c.obj	88f2d8e65714c39e
440	1047	7753259828651835	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_time.c.obj	953c498b1adadd49
423	1064	7753259828481794	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_sleep.c.obj	5cf2d9b9c07da15e
392	1079	7753259828171732	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_clk.c.obj	ba32363c553f7015
408	1097	7753259828331757	esp-idf/esp_hw_support/CMakeFiles/__idf_esp_hw_support.dir/port/esp32c3/rtc_init.c.obj	6117e351300cbe3d
573	1115	7753259829972129	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_rtc_calib.c.obj	ce90180a239d05b
537	1132	7753259829622056	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_table.c.obj	fe1a0931a37d6b57
555	1147	7753259829802096	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_fields.c.obj	8fc06b614024f89
524	1160	7753259829502065	esp-idf/log/liblog.a	fe2b739ec423e137
624	1179	7753259830482252	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_fields.c.obj	26e5983b80ca800c
588	1195	7753259830132169	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/esp32c3/esp_efuse_utility.c.obj	cede0d3902475d80
606	1214	7753259830312222	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_api.c.obj	eddb1aa078ae624d
827	1662	7753259832517299	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/esp_efuse_utility.c.obj	ff36edc5475c4bf2
1179	1682	7753259836038375	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random_esp32c3.c.obj	49e9e9fbac3d2cc6
1079	1696	7753259835048157	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_mem.c.obj	4394466c369f198b
1115	1712	7753259835398245	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_efuse.c.obj	698d737a68f0a1d8
1147	1732	7753259835718310	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/secure_boot.c.obj	241630bfcf729afc
1064	1747	7753259834888118	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_init.c.obj	fe342b02e74d6afb
1097	1765	7753259835228196	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_random.c.obj	cbd651149b2cb169
1048	1781	7753259834728087	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common_loader.c.obj	f4f52ffd00d2f4ef
1214	1893	7753259836388539	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/flash_qio_mode.c.obj	25d3c6fa37f92267
1033	1920	7753259834578054	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_common.c.obj	f033473e85a84d38
1161	1935	7753259835858345	esp-idf/esp_rom/libesp_rom.a	32b412aa87dd51b4
1132	1943	7753259835568284	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_encrypt.c.obj	4e40c4e2e2df620a
1195	2000	7753259836208481	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash.c.obj	54ac8e2b2f285178
1018	2102	7753259834428024	esp-idf/efuse/CMakeFiles/__idf_efuse.dir/src/efuse_controller/keys/with_key_purposes/esp_efuse_api_key.c.obj	23146cbd7e327ee3
1748	2190	7753259841729897	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_clock_loader.c.obj	b1bf21cce72e6ff9
1765	2258	7753259841902815	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console.c.obj	a470e45fce1e9393
1781	2274	7753259842059965	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_console_loader.c.obj	4d46a0b3d70d48c2
1662	2300	7753259840869700	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/bootloader_flash/src/bootloader_flash_config_esp32c3.c.obj	48a88e9ebb86f8e4
1921	2324	7753259843450283	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_soc.c.obj	3bac9c70bd41b28e
1893	2342	7753259843180225	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_sha.c.obj	4671c0eca56bdd24
1696	2357	7753259841209791	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/flash_partitions.c.obj	c4aed1deaef6fbb5
2000	2440	7753259844245851	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_panic.c.obj	371043d7e43101a3
1732	2464	7753259841569864	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_init.c.obj	4fe25f4054a9fe21
2102	2505	7753259845276082	esp-idf/esp_bootloader_format/CMakeFiles/__idf_esp_bootloader_format.dir/esp_bootloader_desc.c.obj	2436b3fba4fc9b02
1935	2597	7753259843600320	esp-idf/esp_common/libesp_common.a	69bea9b40d86bb8a
1712	2644	7753259841369814	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp_image_format.c.obj	1ad156bfad3007fc
2258	2669	7753259846826437	esp-idf/hal/CMakeFiles/__idf_hal.dir/hal_utils.c.obj	7a70e5919f8f8c4a
1943	2684	7753259843680336	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/esp32c3/bootloader_esp32c3.c.obj	428e14fa34f35797
2275	2699	7753259846996491	esp-idf/hal/CMakeFiles/__idf_hal.dir/efuse_hal.c.obj	d9c73e250e045db3
2190	2716	7753259846146280	esp-idf/spi_flash/CMakeFiles/__idf_spi_flash.dir/spi_flash_wrap.c.obj	4bc85f735ee751ab
2301	2763	7753259847256554	esp-idf/hal/CMakeFiles/__idf_hal.dir/esp32c3/efuse_hal.c.obj	80ffd329b68e65e8
2464	2781	7753259848890774	esp-idf/soc/CMakeFiles/__idf_soc.dir/lldesc.c.obj	89e75e9cd4af9572
2358	2828	7753259847830525	esp-idf/hal/CMakeFiles/__idf_hal.dir/cache_hal.c.obj	8c4abbc62e23a5fc
1682	2844	7753259841069750	esp-idf/bootloader_support/CMakeFiles/__idf_bootloader_support.dir/src/bootloader_utility.c.obj	8a6bbef15d0628b4
2505	2858	7753259849300862	esp-idf/soc/CMakeFiles/__idf_soc.dir/dport_access_common.c.obj	a874c8d017429cdb
2326	2874	7753259847515343	esp-idf/hal/CMakeFiles/__idf_hal.dir/wdt_hal_iram.c.obj	6ca5876b8f71aae4
2342	2893	7753259847670486	esp-idf/hal/CMakeFiles/__idf_hal.dir/mmu_hal.c.obj	5f1792f5df4b7e2e
2644	2933	7753259850691172	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/interrupts.c.obj	31f70329aeb04070
2669	2997	7753259850941234	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gpio_periph.c.obj	a061659f6398e4f6
2716	3019	7753259851411336	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/dedic_gpio_periph.c.obj	4f37d4a02e7d0249
2844	3039	7753259852691626	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/rmt_periph.c.obj	bf276d663340ad7b
2699	3061	7753259851241299	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/adc_periph.c.obj	165e6058b97dc64e
2685	3078	7753259851091273	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/uart_periph.c.obj	f89c116343a1b29f
2764	3101	7753259851881452	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/gdma_periph.c.obj	bc7fd951bb0cb5ad
2598	3118	7753259850221088	esp-idf/esp_hw_support/libesp_hw_support.a	3679b8a92846a693
2858	3132	7753259852831659	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/sdm_periph.c.obj	ac2e655e0fae1903
2781	3133	7753259852061487	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/spi_periph.c.obj	530ce939082b6841
2933	3134	7753259853581907	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/temperature_sensor_periph.c.obj	5e20081e045c61cb
2828	3149	7753259852531651	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/ledc_periph.c.obj	5d478cf33beeb1ec
2875	3155	7753259852991699	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2s_periph.c.obj	778b326b9de37a92
2894	3178	7753259853191742	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/i2c_periph.c.obj	bc9a654625a7d45f
2998	3189	7753259854224690	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/timer_periph.c.obj	2905f6afb6d66aca
3078	3225	7753259856425196	project_elf_src_esp32c3.c	65d6441927cf4379
3078	3225	7753259856425196	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/project_elf_src_esp32c3.c	65d6441927cf4379
3041	3240	7753259854654823	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/twai_periph.c.obj	c9717b403e693f23
3020	3261	7753259854444763	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/mpi_periph.c.obj	b88b2cc06ab600e6
3061	3277	7753259854874843	esp-idf/soc/CMakeFiles/__idf_soc.dir/esp32c3/wdt_periph.c.obj	d77882303108f5f9
3119	3303	7753259855434969	esp-idf/esp_system/libesp_system.a	1dd941b058717bae
3101	3312	7753259855254971	esp-idf/main/CMakeFiles/__idf_main.dir/bootloader_start.c.obj	3de4ff7f67755043
3226	3330	7753259856505210	CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c3.c.obj	dd07ff9225bd0dee
3303	3455	7753259857285397	esp-idf/efuse/libefuse.a	34285ec1d70ea8cb
2441	3533	7753259848660717	esp-idf/micro-ecc/CMakeFiles/__idf_micro-ecc.dir/uECC_verify_antifault.c.obj	5e498b4236ac3160
3455	3623	7753259858798659	esp-idf/bootloader_support/libbootloader_support.a	c689d8cbdf8b1dda
3623	3722	7753259860479035	esp-idf/esp_bootloader_format/libesp_bootloader_format.a	e2b02ec7afbd18ba
3722	3826	7753259861465110	esp-idf/spi_flash/libspi_flash.a	d19c898fb21ddba1
3826	3940	7753259862505203	esp-idf/hal/libhal.a	1de4a6772283e9d
3940	4042	7753259863645463	esp-idf/micro-ecc/libmicro-ecc.a	10f520ca19390289
4043	4181	7753259864675700	esp-idf/soc/libsoc.a	2b6f598c40608a33
4181	4275	7753259866066005	esp-idf/main/libmain.a	1e7e64b3c11dcb7c
4275	4404	7753259866996225	bootloader.elf	df4d6bbecb57386d
4404	4727	7753259871476304	.bin_timestamp	e671271a776eabd9
4404	4727	7753259871476304	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/.bin_timestamp	e671271a776eabd9
4728	4817	7753259871526321	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
4728	4817	7753259871526321	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
17	88	7755065000440595	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
17	88	7755065000440595	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
12	86	7755066414323220	esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
12	86	7755066414323220	E:/esp32_space/ble_mill_monitor_slave/build/bootloader/esp-idf/esptool_py/CMakeFiles/bootloader_check_size	b0c8e31d6a210844
